<?php

namespace App\Livewire;

use App\Models\Share;
use App\Models\User;
use App\Models\Reaction;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class SharedPostCard extends Component
{
    public Share $share;
    public $showComments = false;
    public $userReaction = null;
    public $reactionCounts = [];
    public $totalReactions = 0;
    public $totalComments = 0;
    public $totalShares = 0;

    protected $listeners = [
        'reactionUpdated' => 'refreshReactions',
        'commentAdded' => 'refreshComments',
        'commentDeleted' => 'refreshComments',
    ];

    public function mount(Share $share)
    {
        $this->share = $share;
        $this->loadReactionData();
        $this->loadCounts();
    }

    public function loadReactionData()
    {
        if (Auth::check()) {
            $this->userReaction = $this->share->reactions()
                ->where('user_id', Auth::id())
                ->first();
        }

        // Get reaction counts
        $this->reactionCounts = $this->share->reactions()
            ->selectRaw('type, COUNT(*) as count')
            ->groupBy('type')
            ->pluck('count', 'type')
            ->toArray();

        $this->totalReactions = array_sum($this->reactionCounts);
    }

    public function loadCounts()
    {
        $this->totalComments = $this->share->comments()->count();
        $this->totalShares = $this->share->post->shares()->count();
    }

    public function toggleReaction($reactionType = null)
    {
        if (!Auth::check()) {
            return;
        }

        $user = Auth::user();
        $existingReaction = $this->share->reactions()
            ->where('user_id', $user->id)
            ->first();

        if ($reactionType === null) {
            // Remove reaction if it exists
            if ($existingReaction) {
                $existingReaction->delete();
            }
        } else {
            // Create or update reaction
            if ($existingReaction) {
                if ($existingReaction->type === $reactionType) {
                    // Same reaction - remove it
                    $existingReaction->delete();
                } else {
                    // Different reaction - update it
                    $existingReaction->update(['type' => $reactionType]);
                }
            } else {
                // Create new reaction
                $this->share->reactions()->create([
                    'user_id' => $user->id,
                    'type' => $reactionType,
                ]);

                // Fire event for new reaction
                event(new \App\Events\ShareReactionAdded($user, $this->share, $reactionType));
            }
        }

        $this->loadReactionData();
        $this->dispatch('reactionUpdated', $this->share->id);
    }

    public function toggleComments()
    {
        $this->showComments = !$this->showComments;
    }

    public function refreshReactions()
    {
        $this->loadReactionData();
    }

    public function refreshComments()
    {
        $this->loadCounts();
    }

    public function getTopReactionsProperty()
    {
        return collect($this->reactionCounts)
            ->sortDesc()
            ->take(3)
            ->keys()
            ->toArray();
    }

    public function render()
    {
        return view('livewire.shared-post-card');
    }
}
