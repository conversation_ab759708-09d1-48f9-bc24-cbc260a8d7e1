<?php

namespace App\Livewire;

use App\Models\Share;
use App\Models\Reaction;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class SharedPostReactions extends Component
{
    public Share $share;
    public $userReaction = null;
    public $reactionCounts = [];
    public $totalReactions = 0;
    public $showReactionPopup = false;

    protected $listeners = [
        'reactionUpdated' => 'refreshReactions',
    ];

    public function mount(Share $share)
    {
        $this->share = $share;
        $this->loadReactionData();
    }

    public function loadReactionData()
    {
        if (Auth::check()) {
            $this->userReaction = $this->share->reactions()
                ->where('user_id', Auth::id())
                ->first();
        }

        // Get reaction counts grouped by type
        $this->reactionCounts = $this->share->reactions()
            ->selectRaw('type, COUNT(*) as count')
            ->groupBy('type')
            ->pluck('count', 'type')
            ->toArray();

        $this->totalReactions = array_sum($this->reactionCounts);
    }

    public function toggleReaction($reactionType = null)
    {
        if (!Auth::check()) {
            return;
        }

        $user = Auth::user();
        $existingReaction = $this->share->reactions()
            ->where('user_id', $user->id)
            ->first();

        if ($reactionType === null) {
            // Remove reaction if it exists
            if ($existingReaction) {
                $existingReaction->delete();
            }
        } else {
            // Create or update reaction
            if ($existingReaction) {
                if ($existingReaction->type === $reactionType) {
                    // Same reaction - remove it (toggle off)
                    $existingReaction->delete();
                } else {
                    // Different reaction - update it
                    $existingReaction->update(['type' => $reactionType]);
                }
            } else {
                // Create new reaction
                $reaction = $this->share->reactions()->create([
                    'user_id' => $user->id,
                    'type' => $reactionType,
                ]);

                // Fire event for new reaction
                event(new \App\Events\ShareReactionAdded($user, $this->share, $reaction));
            }
        }

        $this->loadReactionData();
        $this->dispatch('reactionUpdated', $this->share->id);
        $this->showReactionPopup = false;
    }

    public function showPopup()
    {
        $this->showReactionPopup = true;
    }

    public function hidePopup()
    {
        $this->showReactionPopup = false;
    }

    public function getTopReactionsProperty()
    {
        return collect($this->reactionCounts)
            ->sortDesc()
            ->take(3)
            ->keys()
            ->toArray();
    }

    public function getAvailableReactionsProperty()
    {
        return [
            'like' => ['emoji' => '👍', 'label' => 'Like', 'color' => 'text-blue-600'],
            'love' => ['emoji' => '❤️', 'label' => 'Love', 'color' => 'text-red-600'],
            'haha' => ['emoji' => '😂', 'label' => 'Haha', 'color' => 'text-yellow-600'],
            'wow' => ['emoji' => '😮', 'label' => 'Wow', 'color' => 'text-yellow-600'],
            'sad' => ['emoji' => '😢', 'label' => 'Sad', 'color' => 'text-yellow-600'],
            'angry' => ['emoji' => '😡', 'label' => 'Angry', 'color' => 'text-red-600'],
        ];
    }

    public function refreshReactions()
    {
        $this->loadReactionData();
    }

    public function render()
    {
        return view('livewire.shared-post-reactions');
    }
}
