<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\Share;
use App\Models\Comment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Notifications\Messages\BroadcastMessage;

class ShareCommented extends Notification implements ShouldQueue, ShouldBroadcast
{
    use Queueable;

    public User $commenter;
    public Share $share;
    public Comment $comment;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $commenter, Share $share, Comment $comment)
    {
        $this->commenter = $commenter;
        $this->share = $share;
        $this->comment = $comment;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $actionText = $this->comment->parent_id ? 'replied to a comment on' : 'commented on';
        
        return (new MailMessage)
                    ->line($this->commenter->name . ' ' . $actionText . ' your shared post.')
                    ->action('View Share', route('posts.show', $this->share->post))
                    ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        $actionText = $this->comment->parent_id ? 'replied to a comment on' : 'commented on';
        
        return [
            'type' => 'share_comment',
            'user_id' => $this->commenter->id,
            'user_name' => $this->commenter->name,
            'user_avatar' => $this->commenter->avatar,
            'share_id' => $this->share->id,
            'post_id' => $this->share->post->id,
            'post_title' => $this->share->post->title,
            'comment_id' => $this->comment->id,
            'comment_content' => \Illuminate\Support\Str::limit($this->comment->content, 100),
            'is_reply' => $this->comment->parent_id !== null,
            'parent_comment_id' => $this->comment->parent_id,
            'message' => $this->commenter->name . ' ' . $actionText . ' your shared post.',
            'created_at' => now(),
        ];
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast(object $notifiable): BroadcastMessage
    {
        return new BroadcastMessage($this->toArray($notifiable));
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return ['private-App.Models.User.' . $this->share->user_id];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'notification';
    }
}
