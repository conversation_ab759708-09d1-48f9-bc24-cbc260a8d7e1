<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Post;
use App\Models\Share;
use App\Models\Comment;
use App\Models\Reaction;
use App\Events\ShareReactionAdded;
use App\Events\ShareCommentAdded;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;
use Livewire\Livewire;

class SharedPostSystemTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $post;
    protected $share;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->post = Post::factory()->create();
        $this->share = Share::factory()->create([
            'user_id' => $this->user->id,
            'post_id' => $this->post->id,
            'message' => 'Test share message'
        ]);
    }

    /** @test */
    public function shared_post_card_component_renders_correctly()
    {
        $this->actingAs($this->user);

        Livewire::test('shared-post-card', ['share' => $this->share])
            ->assertSee($this->user->name)
            ->assertSee('shared a post')
            ->assertSee($this->post->title)
            ->assertSee('Test share message');
    }

    /** @test */
    public function user_can_react_to_shared_post()
    {
        Event::fake();
        $this->actingAs($this->user);

        Livewire::test('shared-post-card', ['share' => $this->share])
            ->call('toggleReaction', 'like')
            ->assertSet('totalReactions', 1);

        $this->assertDatabaseHas('reactions', [
            'reactable_type' => Share::class,
            'reactable_id' => $this->share->id,
            'user_id' => $this->user->id,
            'type' => 'like'
        ]);

        Event::assertDispatched(ShareReactionAdded::class);
    }

    /** @test */
    public function user_can_toggle_reaction_on_shared_post()
    {
        $this->actingAs($this->user);

        // First reaction
        Livewire::test('shared-post-card', ['share' => $this->share])
            ->call('toggleReaction', 'like')
            ->assertSet('totalReactions', 1);

        // Toggle off (remove reaction)
        Livewire::test('shared-post-card', ['share' => $this->share])
            ->call('toggleReaction', 'like')
            ->assertSet('totalReactions', 0);

        $this->assertDatabaseMissing('reactions', [
            'reactable_type' => Share::class,
            'reactable_id' => $this->share->id,
            'user_id' => $this->user->id,
            'type' => 'like'
        ]);
    }

    /** @test */
    public function user_can_comment_on_shared_post()
    {
        Event::fake();
        $this->actingAs($this->user);

        Livewire::test('shared-post-comments', ['share' => $this->share])
            ->set('newComment', 'This is a test comment')
            ->call('addComment');

        $this->assertDatabaseHas('comments', [
            'commentable_type' => Share::class,
            'commentable_id' => $this->share->id,
            'user_id' => $this->user->id,
            'content' => 'This is a test comment'
        ]);

        Event::assertDispatched(ShareCommentAdded::class);
    }

    /** @test */
    public function user_can_reply_to_shared_post_comment()
    {
        $this->actingAs($this->user);

        $comment = Comment::factory()->create([
            'commentable_type' => Share::class,
            'commentable_id' => $this->share->id,
            'user_id' => $this->user->id,
            'content' => 'Parent comment'
        ]);

        Livewire::test('shared-post-comments', ['share' => $this->share])
            ->call('startReply', $comment->id)
            ->set('replyContent', 'This is a reply')
            ->call('addReply');

        $this->assertDatabaseHas('comments', [
            'commentable_type' => Share::class,
            'commentable_id' => $this->share->id,
            'user_id' => $this->user->id,
            'content' => 'This is a reply',
            'parent_id' => $comment->id
        ]);
    }

    /** @test */
    public function user_can_edit_their_shared_post_comment()
    {
        $this->actingAs($this->user);

        $comment = Comment::factory()->create([
            'commentable_type' => Share::class,
            'commentable_id' => $this->share->id,
            'user_id' => $this->user->id,
            'content' => 'Original comment'
        ]);

        Livewire::test('shared-post-comments', ['share' => $this->share])
            ->call('startEdit', $comment->id)
            ->set('editContent', 'Edited comment')
            ->call('saveEdit');

        $this->assertDatabaseHas('comments', [
            'id' => $comment->id,
            'content' => 'Edited comment'
        ]);
    }

    /** @test */
    public function user_can_delete_their_shared_post_comment()
    {
        $this->actingAs($this->user);

        $comment = Comment::factory()->create([
            'commentable_type' => Share::class,
            'commentable_id' => $this->share->id,
            'user_id' => $this->user->id,
            'content' => 'Comment to delete'
        ]);

        Livewire::test('shared-post-comments', ['share' => $this->share])
            ->call('deleteComment', $comment->id);

        $this->assertDatabaseMissing('comments', [
            'id' => $comment->id
        ]);
    }

    /** @test */
    public function shared_post_comment_modal_opens_and_closes()
    {
        $this->actingAs($this->user);

        Livewire::test('shared-post-comment-modal', ['share' => $this->share])
            ->assertSet('isOpen', false)
            ->call('openModal')
            ->assertSet('isOpen', true)
            ->call('closeModal')
            ->assertSet('isOpen', false);
    }

    /** @test */
    public function shared_post_reactions_component_shows_correct_counts()
    {
        $this->actingAs($this->user);

        // Create some reactions
        Reaction::factory()->create([
            'reactable_type' => Share::class,
            'reactable_id' => $this->share->id,
            'user_id' => $this->user->id,
            'type' => 'like'
        ]);

        $otherUser = User::factory()->create();
        Reaction::factory()->create([
            'reactable_type' => Share::class,
            'reactable_id' => $this->share->id,
            'user_id' => $otherUser->id,
            'type' => 'love'
        ]);

        Livewire::test('shared-post-reactions', ['share' => $this->share])
            ->assertSet('totalReactions', 2)
            ->assertSet('reactionCounts.like', 1)
            ->assertSet('reactionCounts.love', 1);
    }

    /** @test */
    public function shared_post_displays_distinct_ui_elements()
    {
        $this->actingAs($this->user);

        $response = Livewire::test('shared-post-card', ['share' => $this->share]);

        // Check for distinctive shared post elements
        $response->assertSee('Shared'); // Shared indicator badge
        $response->assertSee('shared a post'); // Share action text
        
        // The component should render with blue styling elements
        $html = $response->payload['effects']['html'];
        $this->assertStringContains('border-l-blue-500', $html); // Blue left border
        $this->assertStringContains('ring-blue-200', $html); // Blue avatar ring
    }
}
