<?php

namespace App\Listeners;

use App\Events\ShareReactionAdded;
use App\Notifications\ShareReacted;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class SendShareReactionNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle the event.
     */
    public function handle(ShareReactionAdded $event): void
    {
        // Don't notify if user reacted to their own shared post
        if ($event->user->id === $event->share->user_id) {
            return;
        }

        // Check if the share owner wants to receive this type of notification
        if (!$event->share->user->wantsNotification('share_reactions')) {
            return;
        }

        // Create the reaction model for the notification
        $reaction = $event->share->reactions()
            ->where('user_id', $event->user->id)
            ->where('type', $event->reactionType)
            ->first();

        if ($reaction) {
            // Send notification to the share owner
            $event->share->user->notify(new ShareReacted($event->user, $event->share, $reaction));
        }
    }
}
