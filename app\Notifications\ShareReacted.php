<?php

namespace App\Notifications;

use App\Models\User;
use App\Models\Share;
use App\Models\Reaction;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Notifications\Messages\BroadcastMessage;

class ShareReacted extends Notification implements ShouldQueue, ShouldBroadcast
{
    use Queueable;

    public User $reactor;
    public Share $share;
    public Reaction $reaction;

    /**
     * Create a new notification instance.
     */
    public function __construct(User $reactor, Share $share, Reaction $reaction)
    {
        $this->reactor = $reactor;
        $this->share = $share;
        $this->reaction = $reaction;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $reactionDetails = Reaction::getReactionDetails($this->reaction->type);
        $reactionLabel = $reactionDetails ? $reactionDetails['label'] : 'reacted to';

        return (new MailMessage)
                    ->line($this->reactor->name . ' ' . $reactionLabel . ' your shared post.')
                    ->action('View Share', route('posts.show', $this->share->post))
                    ->line('Thank you for using our application!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        $reactionDetails = Reaction::getReactionDetails($this->reaction->type);
        
        return [
            'type' => 'share_reaction',
            'user_id' => $this->reactor->id,
            'user_name' => $this->reactor->name,
            'user_avatar' => $this->reactor->avatar,
            'share_id' => $this->share->id,
            'post_id' => $this->share->post->id,
            'post_title' => $this->share->post->title,
            'reaction_type' => $this->reaction->type,
            'reaction_emoji' => $reactionDetails ? $reactionDetails['emoji'] : '👍',
            'reaction_label' => $reactionDetails ? $reactionDetails['label'] : 'liked',
            'message' => $this->reactor->name . ' ' . ($reactionDetails ? $reactionDetails['label'] : 'reacted to') . ' your shared post.',
            'created_at' => now(),
        ];
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast(object $notifiable): BroadcastMessage
    {
        return new BroadcastMessage($this->toArray($notifiable));
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return ['private-App.Models.User.' . $this->share->user_id];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'notification';
    }
}
