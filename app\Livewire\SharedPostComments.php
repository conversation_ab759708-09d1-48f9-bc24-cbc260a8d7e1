<?php

namespace App\Livewire;

use App\Models\Share;
use App\Models\Comment;
use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class SharedPostComments extends Component
{
    public Share $share;
    public $newComment = '';
    public $sortBy = 'newest';
    public $showReplies = [];
    public $replyingTo = null;
    public $replyContent = '';
    public $editingComment = null;
    public $editContent = '';

    protected $listeners = [
        'commentAdded' => '$refresh',
        'commentDeleted' => '$refresh',
        'reactionUpdated' => '$refresh',
    ];

    protected $rules = [
        'newComment' => 'required|string|max:1000',
        'replyContent' => 'required|string|max:1000',
        'editContent' => 'required|string|max:1000',
    ];

    public function mount(Share $share)
    {
        $this->share = $share;
    }

    public function addComment()
    {
        if (!Auth::check()) {
            return;
        }

        $this->validate(['newComment' => 'required|string|max:1000']);

        $comment = $this->share->comments()->create([
            'content' => $this->newComment,
            'user_id' => Auth::id(),
        ]);

        // Fire event for new comment
        event(new \App\Events\ShareCommentAdded(Auth::user(), $this->share, $comment));

        $this->newComment = '';
        $this->dispatch('commentAdded', $this->share->id);
    }

    public function addReply()
    {
        if (!Auth::check() || !$this->replyingTo) {
            return;
        }

        $this->validate(['replyContent' => 'required|string|max:1000']);

        $reply = $this->share->comments()->create([
            'content' => $this->replyContent,
            'user_id' => Auth::id(),
            'parent_id' => $this->replyingTo,
        ]);

        // Fire event for new reply
        $parentComment = Comment::find($this->replyingTo);
        event(new \App\Events\ShareCommentAdded(Auth::user(), $this->share, $reply));

        $this->replyContent = '';
        $this->replyingTo = null;
        $this->dispatch('commentAdded', $this->share->id);
    }

    public function startReply($commentId)
    {
        $this->replyingTo = $commentId;
        $this->replyContent = '';
    }

    public function cancelReply()
    {
        $this->replyingTo = null;
        $this->replyContent = '';
    }

    public function startEdit($commentId)
    {
        $comment = Comment::find($commentId);
        if ($comment && ($comment->user_id === Auth::id() || Auth::user()->isAdmin())) {
            $this->editingComment = $commentId;
            $this->editContent = $comment->content;
        }
    }

    public function saveEdit()
    {
        if (!$this->editingComment) {
            return;
        }

        $this->validate(['editContent' => 'required|string|max:1000']);

        $comment = Comment::find($this->editingComment);
        if ($comment && ($comment->user_id === Auth::id() || Auth::user()->isAdmin())) {
            $comment->update(['content' => $this->editContent]);
        }

        $this->editingComment = null;
        $this->editContent = '';
    }

    public function cancelEdit()
    {
        $this->editingComment = null;
        $this->editContent = '';
    }

    public function deleteComment($commentId)
    {
        $comment = Comment::find($commentId);
        if ($comment && ($comment->user_id === Auth::id() || Auth::user()->isAdmin())) {
            $comment->delete();
            $this->dispatch('commentDeleted', $this->share->id);
        }
    }

    public function toggleReplies($commentId)
    {
        if (in_array($commentId, $this->showReplies)) {
            $this->showReplies = array_diff($this->showReplies, [$commentId]);
        } else {
            $this->showReplies[] = $commentId;
        }
    }

    public function toggleCommentReaction($commentId, $reactionType = null)
    {
        if (!Auth::check()) {
            return;
        }

        $comment = Comment::find($commentId);
        if (!$comment) {
            return;
        }

        $user = Auth::user();
        $existingReaction = $comment->reactions()
            ->where('user_id', $user->id)
            ->first();

        if ($reactionType === null) {
            // Remove reaction if it exists
            if ($existingReaction) {
                $existingReaction->delete();
            }
        } else {
            // Create or update reaction
            if ($existingReaction) {
                if ($existingReaction->type === $reactionType) {
                    // Same reaction - remove it
                    $existingReaction->delete();
                } else {
                    // Different reaction - update it
                    $existingReaction->update(['type' => $reactionType]);
                }
            } else {
                // Create new reaction
                $comment->reactions()->create([
                    'user_id' => $user->id,
                    'type' => $reactionType,
                ]);

                // Fire event for new reaction
                event(new \App\Events\CommentReactionAdded($user, $comment, $reactionType));
            }
        }

        $this->dispatch('reactionUpdated', $commentId);
    }

    public function getCommentsProperty()
    {
        $query = $this->share->comments()
            ->with(['user', 'reactions', 'replies.user', 'replies.reactions'])
            ->whereNull('parent_id');

        switch ($this->sortBy) {
            case 'oldest':
                $query->oldest();
                break;
            case 'most_liked':
                $query->withCount('reactions')->orderBy('reactions_count', 'desc');
                break;
            default: // newest
                $query->latest();
                break;
        }

        return $query->get();
    }

    public function render()
    {
        return view('livewire.shared-post-comments');
    }
}
