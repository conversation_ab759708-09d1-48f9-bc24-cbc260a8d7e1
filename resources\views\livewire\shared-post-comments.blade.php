<div class="comment-section bg-white" data-share-id="{{ $share->id }}">
    <!-- Add Comment Form -->
    @auth
        <div class="p-4 border-b border-gray-50">
            <form wire:submit.prevent="addComment" class="flex space-x-3">
                <div class="flex-shrink-0">
                    <img class="h-8 w-8 rounded-full ring-2 ring-white shadow-sm"
                         src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}"
                         alt="{{ auth()->user()->name }}">
                </div>
                <div class="flex-1 min-w-0">
                    <div class="relative">
                        <textarea wire:model="newComment" 
                                  rows="1"
                                  placeholder="Write a comment..."
                                  class="block w-full px-3 py-2 border border-gray-300 rounded-full resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                                  style="min-height: 36px;"></textarea>
                        <div class="absolute right-2 top-1/2 transform -translate-y-1/2">
                            <button type="submit" 
                                    wire:loading.attr="disabled"
                                    class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-full text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
                                <span wire:loading.remove>Post</span>
                                <span wire:loading>
                                    <svg class="animate-spin h-3 w-3" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    @endauth

    <!-- Sort Options -->
    @if($this->comments->count() > 0)
        <div class="px-4 py-2 border-b border-gray-50 bg-gray-50">
            <div class="flex items-center justify-between">
                <h4 class="text-sm font-medium text-gray-900">
                    {{ $this->comments->count() }} {{ Str::plural('comment', $this->comments->count()) }}
                </h4>
                <div class="flex items-center space-x-2">
                    <label class="text-xs text-gray-500">Sort by:</label>
                    <select wire:model="sortBy" class="text-xs border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        <option value="newest">Newest</option>
                        <option value="oldest">Oldest</option>
                        <option value="most_liked">Most Liked</option>
                    </select>
                </div>
            </div>
        </div>
    @endif

    <!-- Comments List -->
    <div class="comments-list divide-y divide-gray-100">
        @forelse($this->comments as $comment)
            <div class="comment-item p-4" data-comment-id="{{ $comment->id }}">
                <div class="flex space-x-3">
                    <div class="flex-shrink-0">
                        <a href="{{ route('profile.user', $comment->user) }}">
                            <img class="h-8 w-8 rounded-full"
                                 src="{{ $comment->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($comment->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($comment->user->name) . '&color=7BC74D&background=EEEEEE' }}"
                                 alt="{{ $comment->user->name }}">
                        </a>
                    </div>
                    <div class="flex-1 min-w-0">
                        <!-- Comment Content -->
                        <div class="bg-gray-100 rounded-2xl px-4 py-2">
                            <div class="flex items-center space-x-2 mb-1">
                                <a href="{{ route('profile.user', $comment->user) }}" class="font-medium text-gray-900 hover:text-blue-600 text-sm">
                                    {{ $comment->user->name }}
                                </a>
                                <time class="text-xs text-gray-500" datetime="{{ $comment->created_at->toISOString() }}">
                                    {{ $comment->created_at->diffForHumans() }}
                                </time>
                            </div>
                            
                            @if($editingComment === $comment->id)
                                <!-- Edit Form -->
                                <form wire:submit.prevent="saveEdit" class="mt-2">
                                    <textarea wire:model="editContent" 
                                              rows="2"
                                              class="block w-full px-3 py-2 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"></textarea>
                                    <div class="flex items-center space-x-2 mt-2">
                                        <button type="submit" 
                                                class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700">
                                            Save
                                        </button>
                                        <button type="button" 
                                                wire:click="cancelEdit"
                                                class="inline-flex items-center px-2 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50">
                                            Cancel
                                        </button>
                                    </div>
                                </form>
                            @else
                                <!-- Comment Content -->
                                <div class="comment-content text-gray-900 text-sm">
                                    {!! nl2br(e($comment->content)) !!}
                                </div>
                            @endif
                        </div>

                        <!-- Comment Actions -->
                        <div class="flex items-center space-x-4 mt-1 ml-4">
                            <!-- Reaction Button -->
                            @php
                                $userCommentReaction = $comment->reactions->where('user_id', auth()->id())->first();
                                $reactionDetails = $userCommentReaction ? \App\Models\Reaction::getReactionDetails($userCommentReaction->type) : null;
                                $reactionColor = $reactionDetails ? 'text-blue-600' : 'text-gray-500 hover:text-blue-600';
                                $reactionText = $reactionDetails ? $reactionDetails['label'] : 'Like';
                            @endphp
                            
                            <button wire:click="toggleCommentReaction({{ $comment->id }}, 'like')" 
                                    class="{{ $reactionColor }} text-xs font-medium hover:underline">
                                {{ $reactionText }}
                            </button>

                            <!-- Reply Button -->
                            <button wire:click="startReply({{ $comment->id }})" 
                                    class="text-gray-500 hover:text-blue-600 text-xs font-medium hover:underline">
                                Reply
                            </button>

                            <!-- Edit/Delete Buttons -->
                            @if(auth()->check() && (auth()->id() === $comment->user_id || auth()->user()->isAdmin()))
                                <button wire:click="startEdit({{ $comment->id }})" 
                                        class="text-gray-500 hover:text-blue-600 text-xs font-medium hover:underline">
                                    Edit
                                </button>
                                <button wire:click="deleteComment({{ $comment->id }})" 
                                        wire:confirm="Are you sure you want to delete this comment?"
                                        class="text-gray-500 hover:text-red-600 text-xs font-medium hover:underline">
                                    Delete
                                </button>
                            @endif

                            <!-- Reaction Count -->
                            @if($comment->reactions->count() > 0)
                                <span class="text-xs text-gray-500">
                                    {{ $comment->reactions->count() }} {{ Str::plural('reaction', $comment->reactions->count()) }}
                                </span>
                            @endif
                        </div>

                        <!-- Reply Form -->
                        @if($replyingTo === $comment->id)
                            <div class="mt-3 ml-4">
                                <form wire:submit.prevent="addReply" class="flex space-x-2">
                                    <div class="flex-shrink-0">
                                        <img class="h-6 w-6 rounded-full"
                                             src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}"
                                             alt="{{ auth()->user()->name }}">
                                    </div>
                                    <div class="flex-1">
                                        <textarea wire:model="replyContent" 
                                                  rows="1"
                                                  placeholder="Write a reply..."
                                                  class="block w-full px-3 py-2 border border-gray-300 rounded-full resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"></textarea>
                                        <div class="flex items-center space-x-2 mt-2">
                                            <button type="submit" 
                                                    class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700">
                                                Reply
                                            </button>
                                            <button type="button" 
                                                    wire:click="cancelReply"
                                                    class="inline-flex items-center px-2 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50">
                                                Cancel
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        @endif

                        <!-- Replies -->
                        @if($comment->replies->count() > 0)
                            <div class="mt-3 ml-4">
                                @if(!in_array($comment->id, $showReplies))
                                    <button wire:click="toggleReplies({{ $comment->id }})" 
                                            class="text-blue-600 hover:text-blue-800 text-xs font-medium">
                                        View {{ $comment->replies->count() }} {{ Str::plural('reply', $comment->replies->count()) }}
                                    </button>
                                @else
                                    <button wire:click="toggleReplies({{ $comment->id }})" 
                                            class="text-blue-600 hover:text-blue-800 text-xs font-medium mb-3">
                                        Hide {{ $comment->replies->count() }} {{ Str::plural('reply', $comment->replies->count()) }}
                                    </button>
                                    
                                    <div class="space-y-3">
                                        @foreach($comment->replies as $reply)
                                            <div class="flex space-x-2">
                                                <div class="flex-shrink-0">
                                                    <a href="{{ route('profile.user', $reply->user) }}">
                                                        <img class="h-6 w-6 rounded-full"
                                                             src="{{ $reply->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($reply->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($reply->user->name) . '&color=7BC74D&background=EEEEEE' }}"
                                                             alt="{{ $reply->user->name }}">
                                                    </a>
                                                </div>
                                                <div class="flex-1">
                                                    <div class="bg-gray-100 rounded-2xl px-3 py-2">
                                                        <div class="flex items-center space-x-2 mb-1">
                                                            <a href="{{ route('profile.user', $reply->user) }}" class="font-medium text-gray-900 hover:text-blue-600 text-xs">
                                                                {{ $reply->user->name }}
                                                            </a>
                                                            <time class="text-xs text-gray-500" datetime="{{ $reply->created_at->toISOString() }}">
                                                                {{ $reply->created_at->diffForHumans() }}
                                                            </time>
                                                        </div>
                                                        <div class="text-gray-900 text-xs">
                                                            {!! nl2br(e($reply->content)) !!}
                                                        </div>
                                                    </div>
                                                    
                                                    <!-- Reply Actions -->
                                                    <div class="flex items-center space-x-3 mt-1 ml-3">
                                                        @php
                                                            $userReplyReaction = $reply->reactions->where('user_id', auth()->id())->first();
                                                            $replyReactionDetails = $userReplyReaction ? \App\Models\Reaction::getReactionDetails($userReplyReaction->type) : null;
                                                            $replyReactionColor = $replyReactionDetails ? 'text-blue-600' : 'text-gray-500 hover:text-blue-600';
                                                            $replyReactionText = $replyReactionDetails ? $replyReactionDetails['label'] : 'Like';
                                                        @endphp
                                                        
                                                        <button wire:click="toggleCommentReaction({{ $reply->id }}, 'like')" 
                                                                class="{{ $replyReactionColor }} text-xs font-medium hover:underline">
                                                            {{ $replyReactionText }}
                                                        </button>

                                                        @if(auth()->check() && (auth()->id() === $reply->user_id || auth()->user()->isAdmin()))
                                                            <button wire:click="deleteComment({{ $reply->id }})" 
                                                                    wire:confirm="Are you sure you want to delete this reply?"
                                                                    class="text-gray-500 hover:text-red-600 text-xs font-medium hover:underline">
                                                                Delete
                                                            </button>
                                                        @endif

                                                        @if($reply->reactions->count() > 0)
                                                            <span class="text-xs text-gray-500">
                                                                {{ $reply->reactions->count() }}
                                                            </span>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @empty
            <div class="no-comments text-gray-500 text-center py-8 px-4">
                <div class="max-w-sm mx-auto">
                    <div class="w-12 h-12 mx-auto mb-3 bg-gray-100 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                    </div>
                    <p class="text-sm text-gray-600">No comments yet.</p>
                    <p class="text-xs text-gray-500 mt-1">Be the first to share your thoughts!</p>
                </div>
            </div>
        @endforelse
    </div>
</div>
