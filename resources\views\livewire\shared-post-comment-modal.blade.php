<!-- Shared Post Comment Modal -->
@if($isOpen)
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50" 
         wire:click="closeModal">
        <div class="relative w-full max-w-4xl bg-white rounded-lg shadow-2xl flex flex-col overflow-hidden" 
             style="height: 90vh; max-height: 90vh;" 
             wire:click.stop>
            
            <!-- <PERSON>dal Header -->
            <div class="flex items-center justify-between p-4 border-b border-gray-200 flex-shrink-0">
                <h3 class="text-lg font-medium text-gray-900">
                    {{ $share->user->name }}'s shared post
                </h3>
                <button wire:click="closeModal" 
                        class="text-gray-400 hover:text-gray-600 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <!-- Modal Content -->
            <div class="flex flex-1 overflow-hidden">
                <!-- Left Side: Original Post Preview -->
                <div class="w-1/2 border-r border-gray-200 flex flex-col">
                    <!-- Share Header -->
                    <div class="p-4 border-b border-gray-100 bg-blue-50">
                        <div class="flex items-center space-x-3">
                            <a href="{{ route('profile.user', $share->user) }}">
                                <img class="h-10 w-10 rounded-full ring-2 ring-blue-200" 
                                     src="{{ $share->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->user->name) . '&color=7BC74D&background=EEEEEE' }}" 
                                     alt="{{ $share->user->name }}">
                            </a>
                            <div class="flex-1">
                                <div class="flex items-center space-x-2">
                                    <a href="{{ route('profile.user', $share->user) }}" class="font-medium text-gray-900 hover:text-blue-600">
                                        {{ $share->user->name }}
                                    </a>
                                    <span class="text-gray-500 text-sm">shared a post</span>
                                </div>
                                <div class="text-sm text-gray-500">
                                    {{ $share->created_at->diffForHumans() }}
                                </div>
                            </div>
                        </div>

                        @if($share->message)
                            <div class="mt-3 text-gray-700">
                                <p>{{ $share->message }}</p>
                            </div>
                        @endif
                    </div>

                    <!-- Original Post Content -->
                    <div class="flex-1 overflow-y-auto p-4">
                        <div class="border border-gray-200 rounded-lg overflow-hidden">
                            <!-- Original Post Header -->
                            <div class="p-3 bg-gray-50 border-b border-gray-200">
                                <div class="flex items-center space-x-2">
                                    @if($share->post->group)
                                        <a href="{{ route('groups.show', $share->post->group) }}">
                                            <img class="h-8 w-8 rounded-full"
                                                 src="{{ $share->post->group->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->group->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->group->name) . '&color=3B82F6&background=DBEAFE' }}"
                                                 alt="{{ $share->post->group->name }}">
                                        </a>
                                        <div>
                                            <div class="flex items-center space-x-1">
                                                <a href="{{ route('profile.user', $share->post->user) }}" class="font-medium text-gray-900 hover:text-blue-600 text-sm">
                                                    {{ $share->post->user->name }}
                                                </a>
                                                <svg class="w-3 h-3 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                                </svg>
                                                <a href="{{ route('groups.show', $share->post->group) }}" class="font-medium text-blue-600 hover:text-blue-800 text-sm">
                                                    {{ $share->post->group->name }}
                                                </a>
                                            </div>
                                            <div class="text-xs text-gray-500">
                                                {{ $share->post->created_at->diffForHumans() }}
                                            </div>
                                        </div>
                                    @elseif($share->post->organization)
                                        <a href="{{ route('organizations.show', $share->post->organization) }}">
                                            <img class="h-8 w-8 rounded-full"
                                                 src="{{ $share->post->organization->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->organization->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->organization->name) . '&color=7BC74D&background=EEEEEE' }}"
                                                 alt="{{ $share->post->organization->name }}">
                                        </a>
                                        <div>
                                            <a href="{{ route('organizations.show', $share->post->organization) }}" class="font-medium text-gray-900 hover:text-green-600 text-sm">
                                                {{ $share->post->organization->name }}
                                            </a>
                                            <div class="text-xs text-gray-500">
                                                {{ $share->post->created_at->diffForHumans() }}
                                            </div>
                                        </div>
                                    @else
                                        <a href="{{ route('profile.user', $share->post->user) }}">
                                            <img class="h-8 w-8 rounded-full"
                                                 src="{{ $share->post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->user->name) . '&color=7BC74D&background=EEEEEE' }}"
                                                 alt="{{ $share->post->user->name }}">
                                        </a>
                                        <div>
                                            <a href="{{ route('profile.user', $share->post->user) }}" class="font-medium text-gray-900 hover:text-blue-600 text-sm">
                                                {{ $share->post->user->name }}
                                            </a>
                                            <div class="text-xs text-gray-500">
                                                {{ $share->post->created_at->diffForHumans() }}
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Original Post Content -->
                            <div class="p-4 bg-white">
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $share->post->title }}</h3>
                                
                                @if($share->post->content)
                                    <div class="text-gray-700 mb-3">
                                        <p>{!! nl2br(e($share->post->content)) !!}</p>
                                    </div>
                                @endif

                                @if($share->post->images && count($share->post->images) > 0)
                                    <div class="grid grid-cols-2 gap-2 mb-3">
                                        @foreach(array_slice($share->post->images, 0, 4) as $index => $image)
                                            <div class="relative {{ count($share->post->images) == 1 ? 'col-span-2' : '' }}">
                                                <img src="{{ \Illuminate\Support\Facades\Storage::disk('public')->url($image) }}" 
                                                     alt="Post image" 
                                                     class="w-full h-32 object-cover rounded cursor-pointer"
                                                     onclick="openImageModal('{{ \Illuminate\Support\Facades\Storage::disk('public')->url($image) }}')">
                                                @if($index == 3 && count($share->post->images) > 4)
                                                    <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded">
                                                        <span class="text-white font-semibold">+{{ count($share->post->images) - 4 }}</span>
                                                    </div>
                                                @endif
                                            </div>
                                        @endforeach
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Side: Comments -->
                <div class="w-1/2 flex flex-col">
                    <!-- Comments Header with Sort -->
                    <div class="p-4 border-b border-gray-200 bg-gray-50">
                        <div class="flex items-center justify-between">
                            <h4 class="font-medium text-gray-900">
                                Comments ({{ $this->comments->count() }})
                            </h4>
                            @if($this->comments->count() > 0)
                                <select wire:model="sortBy" class="text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                                    <option value="newest">Newest</option>
                                    <option value="oldest">Oldest</option>
                                    <option value="most_liked">Most Liked</option>
                                </select>
                            @endif
                        </div>
                    </div>

                    <!-- Comments List -->
                    <div class="flex-1 overflow-y-auto">
                        <div class="divide-y divide-gray-100">
                            @forelse($this->comments as $comment)
                                <div class="p-4" data-comment-id="{{ $comment->id }}">
                                    <div class="flex space-x-3">
                                        <div class="flex-shrink-0">
                                            <a href="{{ route('profile.user', $comment->user) }}">
                                                <img class="h-8 w-8 rounded-full"
                                                     src="{{ $comment->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($comment->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($comment->user->name) . '&color=7BC74D&background=EEEEEE' }}"
                                                     alt="{{ $comment->user->name }}">
                                            </a>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <!-- Comment Content -->
                                            <div class="bg-gray-100 rounded-2xl px-4 py-2">
                                                <div class="flex items-center space-x-2 mb-1">
                                                    <a href="{{ route('profile.user', $comment->user) }}" class="font-medium text-gray-900 hover:text-blue-600 text-sm">
                                                        {{ $comment->user->name }}
                                                    </a>
                                                    <time class="text-xs text-gray-500" datetime="{{ $comment->created_at->toISOString() }}">
                                                        {{ $comment->created_at->diffForHumans() }}
                                                    </time>
                                                </div>

                                                @if($editingComment === $comment->id)
                                                    <!-- Edit Form -->
                                                    <form wire:submit.prevent="saveEdit" class="mt-2">
                                                        <textarea wire:model="editContent"
                                                                  rows="2"
                                                                  class="block w-full px-3 py-2 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"></textarea>
                                                        <div class="flex items-center space-x-2 mt-2">
                                                            <button type="submit"
                                                                    class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700">
                                                                Save
                                                            </button>
                                                            <button type="button"
                                                                    wire:click="cancelEdit"
                                                                    class="inline-flex items-center px-2 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50">
                                                                Cancel
                                                            </button>
                                                        </div>
                                                    </form>
                                                @else
                                                    <!-- Comment Content -->
                                                    <div class="comment-content text-gray-900 text-sm">
                                                        {!! nl2br(e($comment->content)) !!}
                                                    </div>
                                                @endif
                                            </div>

                                            <!-- Comment Actions -->
                                            <div class="flex items-center space-x-4 mt-1 ml-4">
                                                <!-- Reaction Button -->
                                                @php
                                                    $userCommentReaction = $comment->reactions->where('user_id', auth()->id())->first();
                                                    $reactionDetails = $userCommentReaction ? \App\Models\Reaction::getReactionDetails($userCommentReaction->type) : null;
                                                    $reactionColor = $reactionDetails ? 'text-blue-600' : 'text-gray-500 hover:text-blue-600';
                                                    $reactionText = $reactionDetails ? $reactionDetails['label'] : 'Like';
                                                @endphp

                                                <button wire:click="toggleCommentReaction({{ $comment->id }}, 'like')"
                                                        class="{{ $reactionColor }} text-xs font-medium hover:underline">
                                                    {{ $reactionText }}
                                                </button>

                                                <!-- Reply Button -->
                                                <button wire:click="startReply({{ $comment->id }})"
                                                        class="text-gray-500 hover:text-blue-600 text-xs font-medium hover:underline">
                                                    Reply
                                                </button>

                                                <!-- Edit/Delete Buttons -->
                                                @if(auth()->check() && (auth()->id() === $comment->user_id || auth()->user()->isAdmin()))
                                                    <button wire:click="startEdit({{ $comment->id }})"
                                                            class="text-gray-500 hover:text-blue-600 text-xs font-medium hover:underline">
                                                        Edit
                                                    </button>
                                                    <button wire:click="deleteComment({{ $comment->id }})"
                                                            wire:confirm="Are you sure you want to delete this comment?"
                                                            class="text-gray-500 hover:text-red-600 text-xs font-medium hover:underline">
                                                        Delete
                                                    </button>
                                                @endif

                                                <!-- Reaction Count -->
                                                @if($comment->reactions->count() > 0)
                                                    <span class="text-xs text-gray-500">
                                                        {{ $comment->reactions->count() }} {{ Str::plural('reaction', $comment->reactions->count()) }}
                                                    </span>
                                                @endif
                                            </div>

                                            <!-- Reply Form -->
                                            @if($replyingTo === $comment->id)
                                                <div class="mt-3 ml-4">
                                                    <form wire:submit.prevent="addReply" class="flex space-x-2">
                                                        <div class="flex-shrink-0">
                                                            <img class="h-6 w-6 rounded-full"
                                                                 src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}"
                                                                 alt="{{ auth()->user()->name }}">
                                                        </div>
                                                        <div class="flex-1">
                                                            <textarea wire:model="replyContent"
                                                                      rows="1"
                                                                      placeholder="Write a reply..."
                                                                      class="block w-full px-3 py-2 border border-gray-300 rounded-full resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"></textarea>
                                                            <div class="flex items-center space-x-2 mt-2">
                                                                <button type="submit"
                                                                        class="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700">
                                                                    Reply
                                                                </button>
                                                                <button type="button"
                                                                        wire:click="cancelReply"
                                                                        class="inline-flex items-center px-2 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50">
                                                                    Cancel
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </form>
                                                </div>
                                            @endif

                                            <!-- Replies -->
                                            @if($comment->replies->count() > 0)
                                                <div class="mt-3 ml-4">
                                                    @if(!in_array($comment->id, $showReplies))
                                                        <button wire:click="toggleReplies({{ $comment->id }})"
                                                                class="text-blue-600 hover:text-blue-800 text-xs font-medium">
                                                            View {{ $comment->replies->count() }} {{ Str::plural('reply', $comment->replies->count()) }}
                                                        </button>
                                                    @else
                                                        <button wire:click="toggleReplies({{ $comment->id }})"
                                                                class="text-blue-600 hover:text-blue-800 text-xs font-medium mb-3">
                                                            Hide {{ $comment->replies->count() }} {{ Str::plural('reply', $comment->replies->count()) }}
                                                        </button>

                                                        <div class="space-y-3">
                                                            @foreach($comment->replies as $reply)
                                                                <div class="flex space-x-2">
                                                                    <div class="flex-shrink-0">
                                                                        <a href="{{ route('profile.user', $reply->user) }}">
                                                                            <img class="h-6 w-6 rounded-full"
                                                                                 src="{{ $reply->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($reply->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($reply->user->name) . '&color=7BC74D&background=EEEEEE' }}"
                                                                                 alt="{{ $reply->user->name }}">
                                                                        </a>
                                                                    </div>
                                                                    <div class="flex-1">
                                                                        <div class="bg-gray-100 rounded-2xl px-3 py-2">
                                                                            <div class="flex items-center space-x-2 mb-1">
                                                                                <a href="{{ route('profile.user', $reply->user) }}" class="font-medium text-gray-900 hover:text-blue-600 text-xs">
                                                                                    {{ $reply->user->name }}
                                                                                </a>
                                                                                <time class="text-xs text-gray-500" datetime="{{ $reply->created_at->toISOString() }}">
                                                                                    {{ $reply->created_at->diffForHumans() }}
                                                                                </time>
                                                                            </div>
                                                                            <div class="text-gray-900 text-xs">
                                                                                {!! nl2br(e($reply->content)) !!}
                                                                            </div>
                                                                        </div>

                                                                        <!-- Reply Actions -->
                                                                        <div class="flex items-center space-x-3 mt-1 ml-3">
                                                                            @php
                                                                                $userReplyReaction = $reply->reactions->where('user_id', auth()->id())->first();
                                                                                $replyReactionDetails = $userReplyReaction ? \App\Models\Reaction::getReactionDetails($userReplyReaction->type) : null;
                                                                                $replyReactionColor = $replyReactionDetails ? 'text-blue-600' : 'text-gray-500 hover:text-blue-600';
                                                                                $replyReactionText = $replyReactionDetails ? $replyReactionDetails['label'] : 'Like';
                                                                            @endphp

                                                                            <button wire:click="toggleCommentReaction({{ $reply->id }}, 'like')"
                                                                                    class="{{ $replyReactionColor }} text-xs font-medium hover:underline">
                                                                                {{ $replyReactionText }}
                                                                            </button>

                                                                            @if(auth()->check() && (auth()->id() === $reply->user_id || auth()->user()->isAdmin()))
                                                                                <button wire:click="deleteComment({{ $reply->id }})"
                                                                                        wire:confirm="Are you sure you want to delete this reply?"
                                                                                        class="text-gray-500 hover:text-red-600 text-xs font-medium hover:underline">
                                                                                    Delete
                                                                                </button>
                                                                            @endif

                                                                            @if($reply->reactions->count() > 0)
                                                                                <span class="text-xs text-gray-500">
                                                                                    {{ $reply->reactions->count() }}
                                                                                </span>
                                                                            @endif
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            @endforeach
                                                        </div>
                                                    @endif
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <div class="flex-1 flex items-center justify-center p-8">
                                    <div class="text-center">
                                        <div class="w-12 h-12 mx-auto mb-3 bg-gray-100 rounded-full flex items-center justify-center">
                                            <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                            </svg>
                                        </div>
                                        <p class="text-sm text-gray-600">No comments yet.</p>
                                        <p class="text-xs text-gray-500 mt-1">Be the first to share your thoughts!</p>
                                    </div>
                                </div>
                            @endforelse
                        </div>
                    </div>

                    <!-- Add Comment Form - Fixed at bottom -->
                    @auth
                        <div class="flex-shrink-0 p-4 border-t border-gray-200 bg-white">
                            <form wire:submit.prevent="addComment" class="flex space-x-3">
                                <div class="flex-shrink-0">
                                    <img class="h-8 w-8 rounded-full"
                                         src="{{ auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}"
                                         alt="{{ auth()->user()->name }}">
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="relative">
                                        <textarea wire:model="newComment"
                                                  rows="1"
                                                  placeholder="Write a comment..."
                                                  class="block w-full px-3 py-2 border border-gray-300 rounded-full resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                                                  style="min-height: 36px;"></textarea>
                                        <div class="absolute right-2 top-1/2 transform -translate-y-1/2">
                                            <button type="submit"
                                                    wire:loading.attr="disabled"
                                                    class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-full text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50">
                                                <span wire:loading.remove>Post</span>
                                                <span wire:loading>
                                                    <svg class="animate-spin h-3 w-3" fill="none" viewBox="0 0 24 24">
                                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                    </svg>
                                                </span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    @endauth
                </div>
            </div>
        </div>
    </div>
@endif
