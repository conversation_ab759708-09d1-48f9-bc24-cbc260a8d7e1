<div class="bg-white rounded-lg shadow-sm border-l-4 border-l-blue-500 border-t border-r border-b border-gray-200 mb-6 relative" 
     data-share-id="<?php echo e($share->id); ?>">
    
    <!-- Shared Post Indicator -->
    <div class="absolute top-2 right-2 bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full flex items-center space-x-1">
        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
        </svg>
        <span>Shared</span>
    </div>

    <!-- Share Header -->
    <div class="p-4 border-b border-gray-100">
        <div class="flex items-center space-x-3">
            <a href="<?php echo e(route('profile.user', $share->user)); ?>">
                <img class="h-10 w-10 rounded-full ring-2 ring-blue-200" 
                     src="<?php echo e($share->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->user->name) . '&color=7BC74D&background=EEEEEE'); ?>" 
                     alt="<?php echo e($share->user->name); ?>">
            </a>
            <div class="flex-1">
                <div class="flex items-center space-x-2">
                    <a href="<?php echo e(route('profile.user', $share->user)); ?>" class="font-medium text-gray-900 hover:text-blue-600">
                        <?php echo e($share->user->name); ?>

                    </a>
                    <span class="text-gray-500">shared a post</span>
                </div>
                <div class="flex items-center space-x-2 text-sm text-gray-500">
                    <time datetime="<?php echo e($share->created_at->toISOString()); ?>">
                        <?php echo e($share->created_at->diffForHumans()); ?>

                    </time>
                    <span>•</span>
                    <div class="flex items-center space-x-1">
                        <?php
                            $privacyDetails = $share->getPrivacyScopeDetails();
                        ?>
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <?php echo $privacyDetails['icon']; ?>

                        </svg>
                        <span><?php echo e($privacyDetails['label']); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Share Message -->
        <!--[if BLOCK]><![endif]--><?php if($share->message): ?>
            <div class="mt-3 text-gray-700">
                <p><?php echo e($share->message); ?></p>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    <!-- Original Post Content (Embedded with distinct styling) -->
    <div class="mx-4 mb-4 border-2 border-gray-100 rounded-lg overflow-hidden bg-gray-50">
        <!-- Original Post Header -->
        <div class="p-4 bg-white border-b border-gray-100">
            <div class="flex items-center space-x-3">
                <!--[if BLOCK]><![endif]--><?php if($share->post->group): ?>
                    <!-- Group Post Header -->
                    <div class="flex items-center space-x-2">
                        <a href="<?php echo e(route('groups.show', $share->post->group)); ?>">
                            <img class="h-8 w-8 rounded-full"
                                 src="<?php echo e($share->post->group->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->group->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->group->name) . '&color=3B82F6&background=DBEAFE'); ?>"
                                 alt="<?php echo e($share->post->group->name); ?>">
                        </a>
                        <div>
                            <div class="flex items-center space-x-1">
                                <a href="<?php echo e(route('profile.user', $share->post->user)); ?>" class="font-medium text-gray-900 hover:text-blue-600 text-sm">
                                    <?php echo e($share->post->user->name); ?>

                                </a>
                                <svg class="w-3 h-3 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                </svg>
                                <a href="<?php echo e(route('groups.show', $share->post->group)); ?>" class="font-medium text-blue-600 hover:text-blue-800 text-sm">
                                    <?php echo e($share->post->group->name); ?>

                                </a>
                            </div>
                            <div class="text-xs text-gray-500">
                                <?php echo e($share->post->created_at->diffForHumans()); ?>

                            </div>
                        </div>
                    </div>
                <?php elseif($share->post->organization): ?>
                    <!-- Organization Post Header -->
                    <div class="flex items-center space-x-2">
                        <a href="<?php echo e(route('organizations.show', $share->post->organization)); ?>">
                            <img class="h-8 w-8 rounded-full"
                                 src="<?php echo e($share->post->organization->logo ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->organization->logo) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->organization->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                                 alt="<?php echo e($share->post->organization->name); ?>">
                        </a>
                        <div>
                            <a href="<?php echo e(route('organizations.show', $share->post->organization)); ?>" class="font-medium text-gray-900 hover:text-green-600 text-sm">
                                <?php echo e($share->post->organization->name); ?>

                            </a>
                            <div class="text-xs text-gray-500">
                                <?php echo e($share->post->created_at->diffForHumans()); ?>

                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- Regular User Post Header -->
                    <div class="flex items-center space-x-2">
                        <a href="<?php echo e(route('profile.user', $share->post->user)); ?>">
                            <img class="h-8 w-8 rounded-full"
                                 src="<?php echo e($share->post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->user->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                                 alt="<?php echo e($share->post->user->name); ?>">
                        </a>
                        <div>
                            <a href="<?php echo e(route('profile.user', $share->post->user)); ?>" class="font-medium text-gray-900 hover:text-blue-600 text-sm">
                                <?php echo e($share->post->user->name); ?>

                            </a>
                            <div class="text-xs text-gray-500">
                                <?php echo e($share->post->created_at->diffForHumans()); ?>

                            </div>
                        </div>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>

        <!-- Original Post Content -->
        <div class="p-4 bg-white">
            <a href="<?php echo e(route('posts.show', $share->post)); ?>" class="block hover:bg-gray-50 transition-colors rounded p-2 -m-2">
                <div class="flex items-start justify-between">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2 flex-1"><?php echo e($share->post->title); ?></h3>
                    <svg class="w-5 h-5 text-gray-400 ml-2 mt-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                </div>
                
                <!--[if BLOCK]><![endif]--><?php if($share->post->content): ?>
                    <div class="text-gray-700 mb-3">
                        <p><?php echo e(Str::limit($share->post->content, 200)); ?></p>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                <!--[if BLOCK]><![endif]--><?php if($share->post->images && count($share->post->images) > 0): ?>
                    <div class="grid grid-cols-2 gap-2 mb-3">
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = array_slice($share->post->images, 0, 4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="relative <?php echo e(count($share->post->images) == 1 ? 'col-span-2' : ''); ?>">
                                <img src="<?php echo e(\Illuminate\Support\Facades\Storage::disk('public')->url($image)); ?>" 
                                     alt="Post image" 
                                     class="w-full h-32 object-cover rounded">
                                <!--[if BLOCK]><![endif]--><?php if($index == 3 && count($share->post->images) > 4): ?>
                                    <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded">
                                        <span class="text-white font-semibold">+<?php echo e(count($share->post->images) - 4); ?></span>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </a>
        </div>
    </div>

    <!-- Reaction Summary Bar -->
    <!--[if BLOCK]><![endif]--><?php if($totalReactions > 0 || $totalComments > 0 || $totalShares > 0): ?>
        <div class="px-4 py-3 border-t border-gray-200">
            <div class="flex items-center justify-between text-sm text-gray-600">
                <!-- Left: Reaction emojis and count -->
                <div class="flex items-center space-x-2">
                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('shared-post-reactions', ['share' => $share]);

$__html = app('livewire')->mount($__name, $__params, 'shared-summary-'.$share->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                </div>

                <!-- Right: Comments and shares count -->
                <div class="flex items-center space-x-4">
                    <!--[if BLOCK]><![endif]--><?php if($totalComments > 0): ?>
                        <span><?php echo e($totalComments); ?> <?php echo e(Str::plural('comment', $totalComments)); ?></span>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    <!--[if BLOCK]><![endif]--><?php if($totalShares > 0): ?>
                        <span><?php echo e($totalShares); ?> <?php echo e(Str::plural('share', $totalShares)); ?></span>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Action Buttons -->
    <div class="px-4 py-3 border-t border-gray-200">
        <div class="flex items-center justify-between">
            <!-- Reaction Button -->
            <div class="flex-1">
                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('shared-post-reactions', ['share' => $share]);

$__html = app('livewire')->mount($__name, $__params, 'shared-reactions-'.$share->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
            </div>

            <!-- Comment Button -->
            <div class="flex-1">
                <button wire:click="toggleComments"
                        class="flex items-center space-x-1 text-gray-600 hover:text-blue-600 transition-colors duration-200 hover:bg-gray-100 px-3 py-2 rounded-lg text-sm font-medium w-full justify-center">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <span>Comment</span>
                </button>
            </div>

            <!-- Comment Modal Button (for larger screens) -->
            <div class="flex-1 hidden md:block">
                <button onclick="openSharedCommentModal(<?php echo e($share->id); ?>)"
                        class="flex items-center space-x-1 text-gray-600 hover:text-blue-600 transition-colors duration-200 hover:bg-gray-100 px-3 py-2 rounded-lg text-sm font-medium w-full justify-center">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <span>View All</span>
                </button>
            </div>

            <!-- Share Button -->
            <div class="flex-1">
                <button onclick="openShareModal(<?php echo e($share->post->id); ?>)" 
                        class="flex items-center space-x-1 text-gray-600 hover:text-blue-600 transition-colors duration-200 hover:bg-gray-100 px-3 py-2 rounded-lg text-sm font-medium w-full justify-center">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                    </svg>
                    <span>Share</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Inline Comments Section -->
    <!--[if BLOCK]><![endif]--><?php if($showComments): ?>
        <div class="border-t border-gray-200">
            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('shared-post-comments', ['share' => $share]);

$__html = app('livewire')->mount($__name, $__params, 'shared-comments-'.$share->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Comment Modal -->
    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('shared-post-comment-modal', ['share' => $share]);

$__html = app('livewire')->mount($__name, $__params, 'shared-modal-'.$share->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

    <!-- Share Modal (reuse existing) -->
    <?php if (isset($component)) { $__componentOriginal0dc3624784f5ac950932d2feff9a6435 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0dc3624784f5ac950932d2feff9a6435 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.share-modal','data' => ['post' => $share->post]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('share-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['post' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($share->post)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0dc3624784f5ac950932d2feff9a6435)): ?>
<?php $attributes = $__attributesOriginal0dc3624784f5ac950932d2feff9a6435; ?>
<?php unset($__attributesOriginal0dc3624784f5ac950932d2feff9a6435); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0dc3624784f5ac950932d2feff9a6435)): ?>
<?php $component = $__componentOriginal0dc3624784f5ac950932d2feff9a6435; ?>
<?php unset($__componentOriginal0dc3624784f5ac950932d2feff9a6435); ?>
<?php endif; ?>
</div>

<script>
// Open shared post comment modal
function openSharedCommentModal(shareId) {
    // Dispatch Livewire event to open modal
    Livewire.dispatch('openCommentModal', { shareId: shareId });
}

// Close shared post comment modal
function closeSharedCommentModal(shareId) {
    // Dispatch Livewire event to close modal
    Livewire.dispatch('closeCommentModal', { shareId: shareId });
}
</script>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/livewire/shared-post-card.blade.php ENDPATH**/ ?>